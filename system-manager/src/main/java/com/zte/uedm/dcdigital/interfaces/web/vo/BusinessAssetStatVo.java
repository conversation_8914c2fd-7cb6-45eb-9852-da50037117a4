package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 商机资产统计响应VO
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BusinessAssetStatVo {

    /**
     * 时间范围内的汇总统计数据（按时间节点）
     */
    private List<TimeStatData> timeStatList;

    /**
     * 下级地区统计数据（指定时间点的快照）
     */
    private List<AreaStatData> areaStatList;

    /**
     * 商机统计数据（支持汇总数据和下级数据）
     */
    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BusinessStatData {

        /**
         * 数据类型：1-汇总数据，2-下级数据
         */
        private Integer dataType;

        /**
         * 日期（汇总数据时使用）
         */
        private String day;

        /**
         * 地区名（下级数据时使用）
         */
        private String areaName;

        /**
         * 总数
         */
        private Long allNum;

        /**
         * 新增商机数
         */
        private Long projectAddNum;

        /**
         * 投标阶段数
         */
        private Long bidNum;

        /**
         * 启动投标数
         */
        private Long projectStartNum;

        /**
         * 交标阶段数
         */
        private Long subBidNum;

        /**
         * 标前阶段数
         */
        private Long beforeBidNum;

        /**
         * 立项阶段数
         */
        private Long projectApprovalNum;
    }
}
