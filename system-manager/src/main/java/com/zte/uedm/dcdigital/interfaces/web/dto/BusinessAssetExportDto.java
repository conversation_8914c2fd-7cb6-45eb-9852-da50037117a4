package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商机资产统计导出DTO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BusinessAssetExportDto {

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 时间类型：1-天，2-周，3-月，4-年
     */
    private Integer timeType;
}
