package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessAssetExportDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessAssetQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessAssetStatVo;

/**
 * 商机资产领域服务接口
 *
 * <AUTHOR>
 */
public interface BusinessAssetDomainService {

    /**
     * 商机数量统计和变化统计
     *
     * @param queryDto 查询参数
     * @return 统计结果
     */
    BusinessAssetStatVo getStatBusiness(BusinessAssetQueryDto queryDto);

    /**
     * 商机资产数据汇聚
     * 将前一天的日表数据汇聚到周表、月表、年表
     */
    void aggregateBusinessAssetData();

    /**
     * 导出商机统计数据
     *
     * @param exportDto 导出参数
     */
    void exportBusinessAssetStat(BusinessAssetExportDto exportDto);

    /**
     * 处理商机启动投标埋点数据（同步）
     *
     * @param projectId 商机ID
     * @param areaId 地区ID
     */
    void processBusinessStartBuryingPoint(String projectId, String areaId);

    /**
     * 异步处理商机启动投标埋点数据
     *
     * @param projectId 商机ID
     * @param areaId 地区ID
     */
    void processBusinessStartBuryingPointAsync(String projectId, String areaId);
}
