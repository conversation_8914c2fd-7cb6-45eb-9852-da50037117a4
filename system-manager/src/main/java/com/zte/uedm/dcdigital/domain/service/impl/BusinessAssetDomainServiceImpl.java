package com.zte.uedm.dcdigital.domain.service.impl;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetDayEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetMonthEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetWeekEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetYearEntity;
import com.zte.uedm.dcdigital.domain.service.BusinessAssetDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BusinessAssetMapper;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessAssetExportDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessAssetQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessAssetStatVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商机资产领域服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BusinessAssetDomainServiceImpl implements BusinessAssetDomainService {

    @Autowired
    private BusinessAssetMapper businessAssetMapper;

    @Override
    public BusinessAssetStatVo getStatBusiness(BusinessAssetQueryDto queryDto) {
        log.info("Getting business asset statistics, queryDto: {}", queryDto);
        
        BusinessAssetStatVo result = new BusinessAssetStatVo();
        
        try {
            // 根据时间类型查询不同的表
            switch (queryDto.getTimeType()) {
                case 1: // 天
                    result = getBusinessStatByDay(queryDto);
                    break;
                case 2: // 周
                    result = getBusinessStatByWeek(queryDto);
                    break;
                case 3: // 月
                    result = getBusinessStatByMonth(queryDto);
                    break;
                case 4: // 年
                    result = getBusinessStatByYear(queryDto);
                    break;
                default:
                    log.warn("Unsupported time type: {}", queryDto.getTimeType());
                    result.setDataList(new ArrayList<>());
                    result.setJuniorList(new ArrayList<>());
            }
            
            log.info("Business asset statistics completed successfully");
            return result;
            
        } catch (Exception e) {
            log.error("Error getting business asset statistics", e);
            result.setDataList(new ArrayList<>());
            result.setJuniorList(new ArrayList<>());
            return result;
        }
    }

    /**
     * 按天查询商机统计数据
     */
    private BusinessAssetStatVo getBusinessStatByDay(BusinessAssetQueryDto queryDto) {
        BusinessAssetStatVo result = new BusinessAssetStatVo();

        // 1. 生成时间范围内每个时间节点的汇总数据
        List<BusinessAssetStatVo.TimeStatData> timeStatList = generateTimeStatData(queryDto, 1);
        result.setTimeStatList(timeStatList);

        // 2. 查询下级地区数据（基于时间点）
        List<BusinessAssetStatVo.AreaStatData> areaStatList = getAreaStatData(queryDto, 1);
        result.setAreaStatList(areaStatList);

        return result;
    }

    /**
     * 按周查询商机统计数据
     */
    private BusinessAssetStatVo getBusinessStatByWeek(BusinessAssetQueryDto queryDto) {
        BusinessAssetStatVo result = new BusinessAssetStatVo();
        List<BusinessAssetStatVo.BusinessStatData> dataList = new ArrayList<>();

        // 1. 查询时间范围内的汇总统计数据
        List<BusinessAssetWeekEntity> weekEntities = businessAssetMapper.selectChildAreaWeekDataByTimeRange(
                queryDto.getAreaId(), queryDto.getStartTime(), queryDto.getEndTime());

        // 转换汇总数据
        for (BusinessAssetWeekEntity entity : weekEntities) {
            BusinessAssetStatVo.BusinessStatData data = new BusinessAssetStatVo.BusinessStatData();
            data.setDataType(1); // 汇总数据
            data.setDay(entity.getDay());
            data.setAreaName(null);
            data.setAllNum(entity.getAllNum());
            data.setProjectAddNum(entity.getProjectAddNum());
            data.setBidNum(entity.getBidNum());
            data.setProjectStartNum(entity.getProjectStartNum());
            data.setSubBidNum(entity.getSubBidNum());
            data.setBeforeBidNum(entity.getBeforeBidNum());
            data.setProjectApprovalNum(entity.getProjectApprovalNum());
            dataList.add(data);
        }

        // 2. 查询下级数据（基于时间点）
        List<BusinessAssetStatVo.BusinessStatData> juniorDataList = getJuniorBusinessData(queryDto);
        dataList.addAll(juniorDataList);

        result.setDataList(dataList);
        return result;
    }

    /**
     * 按月查询商机统计数据
     */
    private BusinessAssetStatVo getBusinessStatByMonth(BusinessAssetQueryDto queryDto) {
        BusinessAssetStatVo result = new BusinessAssetStatVo();
        List<BusinessAssetStatVo.BusinessStatData> dataList = new ArrayList<>();

        // 1. 查询时间范围内的汇总统计数据
        List<BusinessAssetMonthEntity> monthEntities = businessAssetMapper.selectChildAreaMonthDataByTimeRange(
                queryDto.getAreaId(), queryDto.getStartTime(), queryDto.getEndTime());

        // 转换汇总数据
        for (BusinessAssetMonthEntity entity : monthEntities) {
            BusinessAssetStatVo.BusinessStatData data = new BusinessAssetStatVo.BusinessStatData();
            data.setDataType(1); // 汇总数据
            data.setDay(entity.getDay());
            data.setAreaName(null);
            data.setAllNum(entity.getAllNum());
            data.setProjectAddNum(entity.getProjectAddNum());
            data.setBidNum(entity.getBidNum());
            data.setProjectStartNum(entity.getProjectStartNum());
            data.setSubBidNum(entity.getSubBidNum());
            data.setBeforeBidNum(entity.getBeforeBidNum());
            data.setProjectApprovalNum(entity.getProjectApprovalNum());
            dataList.add(data);
        }

        // 2. 查询下级数据（基于时间点）
        List<BusinessAssetStatVo.BusinessStatData> juniorDataList = getJuniorBusinessData(queryDto);
        dataList.addAll(juniorDataList);

        result.setDataList(dataList);
        return result;
    }

    /**
     * 按年查询商机统计数据
     */
    private BusinessAssetStatVo getBusinessStatByYear(BusinessAssetQueryDto queryDto) {
        BusinessAssetStatVo result = new BusinessAssetStatVo();
        List<BusinessAssetStatVo.BusinessStatData> dataList = new ArrayList<>();

        // 1. 查询时间范围内的汇总统计数据
        List<BusinessAssetYearEntity> yearEntities = businessAssetMapper.selectChildAreaYearDataByTimeRange(
                queryDto.getAreaId(), queryDto.getStartTime(), queryDto.getEndTime());

        // 转换汇总数据
        for (BusinessAssetYearEntity entity : yearEntities) {
            BusinessAssetStatVo.BusinessStatData data = new BusinessAssetStatVo.BusinessStatData();
            data.setDataType(1); // 汇总数据
            data.setDay(entity.getDay());
            data.setAreaName(null);
            data.setAllNum(entity.getAllNum());
            data.setProjectAddNum(entity.getProjectAddNum());
            data.setBidNum(entity.getBidNum());
            data.setProjectStartNum(entity.getProjectStartNum());
            data.setSubBidNum(entity.getSubBidNum());
            data.setBeforeBidNum(entity.getBeforeBidNum());
            data.setProjectApprovalNum(entity.getProjectApprovalNum());
            dataList.add(data);
        }

        // 2. 查询下级数据（基于时间点）
        List<BusinessAssetStatVo.BusinessStatData> juniorDataList = getJuniorBusinessData(queryDto);
        dataList.addAll(juniorDataList);

        result.setDataList(dataList);
        return result;
    }

    /**
     * 获取下级商机数据
     * 根据时间点查询所有子节点在该时间点的数据，并添加汇总行
     */
    private List<BusinessAssetStatVo.BusinessStatData> getJuniorBusinessData(BusinessAssetQueryDto queryDto) {
        log.info("Getting junior business data for parentAreaId: {}, timePoint: {}", queryDto.getAreaId(), queryDto.getTimePoint());

        List<BusinessAssetStatVo.BusinessStatData> juniorList = new ArrayList<>();

        try {
            // 验证时间点参数
            if (queryDto.getTimePoint() == null || queryDto.getTimePoint().trim().isEmpty()) {
                log.warn("Time point is null or empty, using end time as default");
                queryDto.setTimePoint(queryDto.getEndTime());
            }

            // 根据时间类型查询不同的表
            List<? extends Object> childDataList = null;
            switch (queryDto.getTimeType()) {
                case 1: // 天
                    childDataList = businessAssetMapper.selectChildAreaDayDataByTimePoint(queryDto.getAreaId(), queryDto.getTimePoint());
                    break;
                case 2: // 周
                    childDataList = businessAssetMapper.selectChildAreaWeekDataByTimePoint(queryDto.getAreaId(), queryDto.getTimePoint());
                    break;
                case 3: // 月
                    childDataList = businessAssetMapper.selectChildAreaMonthDataByTimePoint(queryDto.getAreaId(), queryDto.getTimePoint());
                    break;
                case 4: // 年
                    childDataList = businessAssetMapper.selectChildAreaYearDataByTimePoint(queryDto.getAreaId(), queryDto.getTimePoint());
                    break;
                default:
                    log.warn("Unsupported time type: {}", queryDto.getTimeType());
                    return juniorList;
            }

            // 汇总数据，用于生成ALL行
            BusinessAssetStatVo.BusinessStatData allData = new BusinessAssetStatVo.BusinessStatData();
            allData.setDataType(2); // 下级数据
            allData.setDay(null);
            allData.setAreaName("ALL");
            allData.setAllNum(0L);
            allData.setProjectAddNum(0L);
            allData.setBidNum(0L);
            allData.setProjectStartNum(0L);
            allData.setSubBidNum(0L);
            allData.setBeforeBidNum(0L);
            allData.setProjectApprovalNum(0L);

            // 处理子节点数据
            if (childDataList != null && !childDataList.isEmpty()) {
                for (Object obj : childDataList) {
                    BusinessAssetStatVo.BusinessStatData juniorData = convertToBusinessStatData(obj);
                    if (juniorData != null) {
                        juniorData.setDataType(2); // 下级数据
                        juniorData.setDay(null);
                        juniorList.add(juniorData);

                        // 累加到汇总数据
                        allData.setAllNum(allData.getAllNum() + (juniorData.getAllNum() != null ? juniorData.getAllNum() : 0L));
                        allData.setProjectAddNum(allData.getProjectAddNum() + (juniorData.getProjectAddNum() != null ? juniorData.getProjectAddNum() : 0L));
                        allData.setBidNum(allData.getBidNum() + (juniorData.getBidNum() != null ? juniorData.getBidNum() : 0L));
                        allData.setProjectStartNum(allData.getProjectStartNum() + (juniorData.getProjectStartNum() != null ? juniorData.getProjectStartNum() : 0L));
                        allData.setSubBidNum(allData.getSubBidNum() + (juniorData.getSubBidNum() != null ? juniorData.getSubBidNum() : 0L));
                        allData.setBeforeBidNum(allData.getBeforeBidNum() + (juniorData.getBeforeBidNum() != null ? juniorData.getBeforeBidNum() : 0L));
                        allData.setProjectApprovalNum(allData.getProjectApprovalNum() + (juniorData.getProjectApprovalNum() != null ? juniorData.getProjectApprovalNum() : 0L));
                    }
                }
            }

            // 将汇总行添加到列表开头
            juniorList.add(0, allData);

            log.info("Junior business data query completed, found {} child areas plus 1 summary row", juniorList.size() - 1);

        } catch (Exception e) {
            log.error("Error getting junior business data", e);
        }

        return juniorList;
    }

    /**
     * 将不同类型的Entity转换为BusinessStatData
     */
    private BusinessAssetStatVo.BusinessStatData convertToBusinessStatData(Object entity) {
        try {
            BusinessAssetStatVo.BusinessStatData businessStatData = new BusinessAssetStatVo.BusinessStatData();

            if (entity instanceof BusinessAssetDayEntity) {
                BusinessAssetDayEntity dayEntity = (BusinessAssetDayEntity) entity;
                businessStatData.setAreaName(getAreaName(dayEntity.getAreaId()));
                businessStatData.setAllNum(dayEntity.getAllNum());
                businessStatData.setProjectAddNum(dayEntity.getProjectAddNum());
                businessStatData.setBidNum(dayEntity.getBidNum());
                businessStatData.setProjectStartNum(dayEntity.getProjectStartNum());
                businessStatData.setSubBidNum(dayEntity.getSubBidNum());
                businessStatData.setBeforeBidNum(dayEntity.getBeforeBidNum());
                businessStatData.setProjectApprovalNum(dayEntity.getProjectApprovalNum());
            } else if (entity instanceof BusinessAssetWeekEntity) {
                BusinessAssetWeekEntity weekEntity = (BusinessAssetWeekEntity) entity;
                businessStatData.setAreaName(getAreaName(weekEntity.getAreaId()));
                businessStatData.setAllNum(weekEntity.getAllNum());
                businessStatData.setProjectAddNum(weekEntity.getProjectAddNum());
                businessStatData.setBidNum(weekEntity.getBidNum());
                businessStatData.setProjectStartNum(weekEntity.getProjectStartNum());
                businessStatData.setSubBidNum(weekEntity.getSubBidNum());
                businessStatData.setBeforeBidNum(weekEntity.getBeforeBidNum());
                businessStatData.setProjectApprovalNum(weekEntity.getProjectApprovalNum());
            } else if (entity instanceof BusinessAssetMonthEntity) {
                BusinessAssetMonthEntity monthEntity = (BusinessAssetMonthEntity) entity;
                businessStatData.setAreaName(getAreaName(monthEntity.getAreaId()));
                businessStatData.setAllNum(monthEntity.getAllNum());
                businessStatData.setProjectAddNum(monthEntity.getProjectAddNum());
                businessStatData.setBidNum(monthEntity.getBidNum());
                businessStatData.setProjectStartNum(monthEntity.getProjectStartNum());
                businessStatData.setSubBidNum(monthEntity.getSubBidNum());
                businessStatData.setBeforeBidNum(monthEntity.getBeforeBidNum());
                businessStatData.setProjectApprovalNum(monthEntity.getProjectApprovalNum());
            } else if (entity instanceof BusinessAssetYearEntity) {
                BusinessAssetYearEntity yearEntity = (BusinessAssetYearEntity) entity;
                businessStatData.setAreaName(getAreaName(yearEntity.getAreaId()));
                businessStatData.setAllNum(yearEntity.getAllNum());
                businessStatData.setProjectAddNum(yearEntity.getProjectAddNum());
                businessStatData.setBidNum(yearEntity.getBidNum());
                businessStatData.setProjectStartNum(yearEntity.getProjectStartNum());
                businessStatData.setSubBidNum(yearEntity.getSubBidNum());
                businessStatData.setBeforeBidNum(yearEntity.getBeforeBidNum());
                businessStatData.setProjectApprovalNum(yearEntity.getProjectApprovalNum());
            } else {
                log.warn("Unknown entity type: {}", entity.getClass().getSimpleName());
                return null;
            }

            return businessStatData;

        } catch (Exception e) {
            log.error("Error converting entity to junior data", e);
            return null;
        }
    }

    /**
     * 获取地区名称
     */
    private String getAreaName(String areaId) {
        try {
            String areaName = businessAssetMapper.selectAreaNameById(areaId);
            return areaName != null ? areaName : "Unknown Area";
        } catch (Exception e) {
            log.warn("Failed to get area name for areaId: {}", areaId);
            return "Unknown Area";
        }
    }

    @Override
    public void aggregateBusinessAssetData() {
        log.info("Starting business asset data aggregation");

        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime yesterday = now.minusDays(1);

            String yesterdayStr = yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String weekStr = yesterday.format(DateTimeFormatter.ofPattern("yyyyww"));
            String monthStr = yesterday.format(DateTimeFormatter.ofPattern("yyyyMM"));
            String yearStr = yesterday.format(DateTimeFormatter.ofPattern("yyyy"));

            // 汇聚周数据
            aggregateWeekData(yesterdayStr, weekStr);

            // 汇聚月数据
            aggregateMonthData(yesterdayStr, monthStr);

            // 汇聚年数据
            aggregateYearData(yesterdayStr, yearStr);

            log.info("Business asset data aggregation completed for date: {}", yesterdayStr);

        } catch (Exception e) {
            log.error("Error during business asset data aggregation", e);
        }
    }

    /**
     * 汇聚周数据
     */
    private void aggregateWeekData(String day, String week) {
        try {
            log.info("Aggregating week data for week: {}", week);

            // 计算周的开始和结束日期
            String weekStart = calculateWeekStart(day);
            String weekEnd = calculateWeekEnd(day);

            // 查询该周的所有日表数据
            List<BusinessAssetDayEntity> dayDataList = businessAssetMapper.selectDayDateByTimeRange(weekStart, weekEnd);

            // 按地区分组汇聚数据
            Map<String, BusinessAssetWeekEntity> weekDataMap = new HashMap<>();

            for (BusinessAssetDayEntity dayEntity : dayDataList) {
                String areaId = dayEntity.getAreaId();
                BusinessAssetWeekEntity weekEntity = weekDataMap.get(areaId);

                if (weekEntity == null) {
                    weekEntity = new BusinessAssetWeekEntity();
                    weekEntity.setId(generateId(areaId, week));
                    weekEntity.setDay(week);
                    weekEntity.setAreaId(areaId);
                    weekEntity.setParentAreaId(dayEntity.getParentAreaId()); // 设置父节点ID
                    weekEntity.setAllNum(0L);
                    weekEntity.setBidNum(0L);
                    weekEntity.setSubBidNum(0L);
                    weekEntity.setBeforeBidNum(0L);
                    weekEntity.setProjectApprovalNum(0L);
                    weekEntity.setProjectAddNum(0L);
                    weekEntity.setProjectStartNum(0L);
                    weekEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    weekDataMap.put(areaId, weekEntity);
                }

                // 汇聚数据
                weekEntity.setAllNum(weekEntity.getAllNum() + (dayEntity.getAllNum() != null ? dayEntity.getAllNum() : 0L));
                weekEntity.setBidNum(weekEntity.getBidNum() + (dayEntity.getBidNum() != null ? dayEntity.getBidNum() : 0L));
                weekEntity.setSubBidNum(weekEntity.getSubBidNum() + (dayEntity.getSubBidNum() != null ? dayEntity.getSubBidNum() : 0L));
                weekEntity.setBeforeBidNum(weekEntity.getBeforeBidNum() + (dayEntity.getBeforeBidNum() != null ? dayEntity.getBeforeBidNum() : 0L));
                weekEntity.setProjectApprovalNum(weekEntity.getProjectApprovalNum() + (dayEntity.getProjectApprovalNum() != null ? dayEntity.getProjectApprovalNum() : 0L));
                weekEntity.setProjectAddNum(weekEntity.getProjectAddNum() + (dayEntity.getProjectAddNum() != null ? dayEntity.getProjectAddNum() : 0L));
                weekEntity.setProjectStartNum(weekEntity.getProjectStartNum() + (dayEntity.getProjectStartNum() != null ? dayEntity.getProjectStartNum() : 0L));
            }

            // 批量插入或更新周表数据
            if (!weekDataMap.isEmpty()) {
                List<BusinessAssetWeekEntity> weekDataList = new ArrayList<>(weekDataMap.values());
                businessAssetMapper.batchInsertWeekData(weekDataList);
                log.info("Aggregated {} week records for week: {}", weekDataList.size(), week);
            }

        } catch (Exception e) {
            log.error("Error aggregating week data for week: {}", week, e);
        }
    }

    /**
     * 汇聚月数据
     */
    private void aggregateMonthData(String day, String month) {
        try {
            log.info("Aggregating month data for month: {}", month);

            // 计算月的开始和结束日期
            String monthStart = month + "01";
            String monthEnd = calculateMonthEnd(month);

            // 查询该月的所有日表数据
            List<BusinessAssetDayEntity> dayDataList = businessAssetMapper.selectDayDateByTimeRange(monthStart, monthEnd);

            // 按地区分组汇聚数据
            Map<String, BusinessAssetMonthEntity> monthDataMap = new HashMap<>();

            for (BusinessAssetDayEntity dayEntity : dayDataList) {
                String areaId = dayEntity.getAreaId();
                BusinessAssetMonthEntity monthEntity = monthDataMap.get(areaId);

                if (monthEntity == null) {
                    monthEntity = new BusinessAssetMonthEntity();
                    monthEntity.setId(generateId(areaId, month));
                    monthEntity.setDay(month);
                    monthEntity.setAreaId(areaId);
                    monthEntity.setParentAreaId(dayEntity.getParentAreaId()); // 设置父节点ID
                    monthEntity.setAllNum(0L);
                    monthEntity.setBidNum(0L);
                    monthEntity.setSubBidNum(0L);
                    monthEntity.setBeforeBidNum(0L);
                    monthEntity.setProjectApprovalNum(0L);
                    monthEntity.setProjectAddNum(0L);
                    monthEntity.setProjectStartNum(0L);
                    monthEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    monthDataMap.put(areaId, monthEntity);
                }

                // 汇聚数据
                monthEntity.setAllNum(monthEntity.getAllNum() + (dayEntity.getAllNum() != null ? dayEntity.getAllNum() : 0L));
                monthEntity.setBidNum(monthEntity.getBidNum() + (dayEntity.getBidNum() != null ? dayEntity.getBidNum() : 0L));
                monthEntity.setSubBidNum(monthEntity.getSubBidNum() + (dayEntity.getSubBidNum() != null ? dayEntity.getSubBidNum() : 0L));
                monthEntity.setBeforeBidNum(monthEntity.getBeforeBidNum() + (dayEntity.getBeforeBidNum() != null ? dayEntity.getBeforeBidNum() : 0L));
                monthEntity.setProjectApprovalNum(monthEntity.getProjectApprovalNum() + (dayEntity.getProjectApprovalNum() != null ? dayEntity.getProjectApprovalNum() : 0L));
                monthEntity.setProjectAddNum(monthEntity.getProjectAddNum() + (dayEntity.getProjectAddNum() != null ? dayEntity.getProjectAddNum() : 0L));
                monthEntity.setProjectStartNum(monthEntity.getProjectStartNum() + (dayEntity.getProjectStartNum() != null ? dayEntity.getProjectStartNum() : 0L));
            }

            // 批量插入或更新月表数据
            if (!monthDataMap.isEmpty()) {
                List<BusinessAssetMonthEntity> monthDataList = new ArrayList<>(monthDataMap.values());
                businessAssetMapper.batchInsertMonthData(monthDataList);
                log.info("Aggregated {} month records for month: {}", monthDataList.size(), month);
            }

        } catch (Exception e) {
            log.error("Error aggregating month data for month: {}", month, e);
        }
    }

    /**
     * 汇聚年数据
     */
    private void aggregateYearData(String day, String year) {
        try {
            log.info("Aggregating year data for year: {}", year);

            // 计算年的开始和结束日期
            String yearStart = year + "0101";
            String yearEnd = year + "1231";

            // 查询该年的所有日表数据
            List<BusinessAssetDayEntity> dayDataList = businessAssetMapper.selectDayDateByTimeRange(yearStart, yearEnd);

            // 按地区分组汇聚数据
            Map<String, BusinessAssetYearEntity> yearDataMap = new HashMap<>();

            for (BusinessAssetDayEntity dayEntity : dayDataList) {
                String areaId = dayEntity.getAreaId();
                BusinessAssetYearEntity yearEntity = yearDataMap.get(areaId);

                if (yearEntity == null) {
                    yearEntity = new BusinessAssetYearEntity();
                    yearEntity.setId(generateId(areaId, year));
                    yearEntity.setDay(year);
                    yearEntity.setAreaId(areaId);
                    yearEntity.setParentAreaId(dayEntity.getParentAreaId()); // 设置父节点ID
                    yearEntity.setAllNum(0L);
                    yearEntity.setBidNum(0L);
                    yearEntity.setSubBidNum(0L);
                    yearEntity.setBeforeBidNum(0L);
                    yearEntity.setProjectApprovalNum(0L);
                    yearEntity.setProjectAddNum(0L);
                    yearEntity.setProjectStartNum(0L);
                    yearEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    yearDataMap.put(areaId, yearEntity);
                }

                // 汇聚数据
                yearEntity.setAllNum(yearEntity.getAllNum() + (dayEntity.getAllNum() != null ? dayEntity.getAllNum() : 0L));
                yearEntity.setBidNum(yearEntity.getBidNum() + (dayEntity.getBidNum() != null ? dayEntity.getBidNum() : 0L));
                yearEntity.setSubBidNum(yearEntity.getSubBidNum() + (dayEntity.getSubBidNum() != null ? dayEntity.getSubBidNum() : 0L));
                yearEntity.setBeforeBidNum(yearEntity.getBeforeBidNum() + (dayEntity.getBeforeBidNum() != null ? dayEntity.getBeforeBidNum() : 0L));
                yearEntity.setProjectApprovalNum(yearEntity.getProjectApprovalNum() + (dayEntity.getProjectApprovalNum() != null ? dayEntity.getProjectApprovalNum() : 0L));
                yearEntity.setProjectAddNum(yearEntity.getProjectAddNum() + (dayEntity.getProjectAddNum() != null ? dayEntity.getProjectAddNum() : 0L));
                yearEntity.setProjectStartNum(yearEntity.getProjectStartNum() + (dayEntity.getProjectStartNum() != null ? dayEntity.getProjectStartNum() : 0L));
            }

            // 批量插入或更新年表数据
            if (!yearDataMap.isEmpty()) {
                List<BusinessAssetYearEntity> yearDataList = new ArrayList<>(yearDataMap.values());
                businessAssetMapper.batchInsertYearData(yearDataList);
                log.info("Aggregated {} year records for year: {}", yearDataList.size(), year);
            }

        } catch (Exception e) {
            log.error("Error aggregating year data for year: {}", year, e);
        }
    }

    /**
     * 计算周开始日期
     */
    private String calculateWeekStart(String day) {
        try {
            LocalDateTime date = LocalDateTime.parse(day + "000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            LocalDateTime weekStart = date.minusDays(date.getDayOfWeek().getValue() - 1);
            return weekStart.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
            log.warn("Error calculating week start for day: {}", day);
            return day;
        }
    }

    /**
     * 计算周结束日期
     */
    private String calculateWeekEnd(String day) {
        try {
            LocalDateTime date = LocalDateTime.parse(day + "000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            LocalDateTime weekEnd = date.plusDays(7 - date.getDayOfWeek().getValue());
            return weekEnd.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
            log.warn("Error calculating week end for day: {}", day);
            return day;
        }
    }

    /**
     * 计算月结束日期
     */
    private String calculateMonthEnd(String month) {
        try {
            LocalDateTime date = LocalDateTime.parse(month + "01000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            LocalDateTime monthEnd = date.plusMonths(1).minusDays(1);
            return monthEnd.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
            log.warn("Error calculating month end for month: {}", month);
            return month + "31";
        }
    }

    @Override
    public void exportBusinessAssetStat(BusinessAssetExportDto exportDto) {
        log.info("Exporting business asset statistics, exportDto: {}", exportDto);

        try {
            // 构建查询条件
            BusinessAssetQueryDto queryDto = new BusinessAssetQueryDto();
            queryDto.setAreaId(exportDto.getAreaId());
            queryDto.setStartTime(exportDto.getStartTime());
            queryDto.setEndTime(exportDto.getEndTime());
            queryDto.setTimeType(exportDto.getTimeType());
            // 导出时包含汇总数据和下级数据

            // 获取统计数据
            BusinessAssetStatVo statData = getStatBusiness(queryDto);

            // TODO: 实现Excel导出逻辑
            // 这里需要使用Apache POI或其他Excel库来生成Excel文件
            // 可以参考其他模块的导出实现

            log.info("Business asset statistics export completed, dataList size: {}",
                    statData.getDataList() != null ? statData.getDataList().size() : 0);

        } catch (Exception e) {
            log.error("Error exporting business asset statistics", e);
        }
    }

    @Override
    public void processBusinessStartBuryingPoint(String projectId, String areaId) {
        log.info("Processing business start burying point, projectId: {}, areaId: {}", projectId, areaId);

        try {
            // 参数验证
            if (projectId == null || projectId.trim().isEmpty()) {
                log.warn("Project ID is null or empty, skipping burying point processing");
                return;
            }

            if (areaId == null || areaId.trim().isEmpty()) {
                log.warn("Area ID is null or empty, skipping burying point processing");
                return;
            }

            // 获取当前日期
            String currentDay = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 插入启动投标记录
            businessAssetMapper.insertBusinessStartRecord(projectId, areaId, currentDay);

            // 更新当日统计数据
            updateDayStatistics(areaId, currentDay);

            log.info("Business start burying point processed successfully for projectId: {}, areaId: {}", projectId, areaId);

        } catch (Exception e) {
            log.error("Error processing business start burying point for projectId: {}, areaId: {}", projectId, areaId, e);
        }
    }

    /**
     * 更新当日统计数据
     */
    private void updateDayStatistics(String areaId, String day) {
        try {
            // 检查当日数据是否存在
            Integer exists = businessAssetMapper.checkDayDataExists(day, areaId);

            if (exists != null && exists > 0) {
                // 数据存在，更新启动投标数量
                Long startCount = businessAssetMapper.countBusinessStartRecordsByDay(day, areaId);

                // 查询现有数据
                List<BusinessAssetDayEntity> existingData = businessAssetMapper.selectDayDateByAreaIdAndTimeRange(areaId, day, day);
                if (!existingData.isEmpty()) {
                    BusinessAssetDayEntity entity = existingData.get(0);
                    entity.setProjectStartNum(startCount);
                    entity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    businessAssetMapper.updateDayData(entity);
                }
            } else {
                // 数据不存在，创建新记录
                BusinessAssetDayEntity newEntity = new BusinessAssetDayEntity();
                newEntity.setId(generateId(areaId, day));
                newEntity.setDay(day);
                newEntity.setAreaId(areaId);
                newEntity.setAllNum(0L);
                newEntity.setBidNum(0L);
                newEntity.setSubBidNum(0L);
                newEntity.setBeforeBidNum(0L);
                newEntity.setProjectApprovalNum(0L);
                newEntity.setProjectAddNum(0L);
                newEntity.setProjectStartNum(1L); // 新增启动投标
                newEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                List<BusinessAssetDayEntity> insertList = new ArrayList<>();
                insertList.add(newEntity);
                businessAssetMapper.batchInsertDayData(insertList);
            }

        } catch (Exception e) {
            log.error("Error updating day statistics for areaId: {}, day: {}", areaId, day, e);
        }
    }

    /**
     * 生成ID
     */
    private String generateId(String areaId, String day) {
        return areaId + "_" + day + "_" + System.currentTimeMillis();
    }

    @Override
    @Async
    public void processBusinessStartBuryingPointAsync(String projectId, String areaId) {
        log.info("Processing business start burying point asynchronously, projectId: {}, areaId: {}", projectId, areaId);
        
        try {
            // 异步处理埋点数据
            processBusinessStartBuryingPoint(projectId, areaId);
            
        } catch (Exception e) {
            log.error("Error processing business start burying point asynchronously", e);
        }
    }
}
