# 商机数量统计接口框架说明

## 概述

根据资产统计方案文档，已实现商机数量统计接口的基础框架。该框架遵循DDD（领域驱动设计）架构模式，包含完整的Controller、Service、DTO、VO等层次。

## 已实现的文件结构

### 1. Controller层
- `BusinessAssetController.java` - 商机资产统计控制器
- `AssetStatController.java` - 资产统计通用控制器（处理埋点接口）

### 2. Service层
- `BusinessAssetDomainService.java` - 商机资产领域服务接口
- `BusinessAssetDomainServiceImpl.java` - 商机资产领域服务实现类

### 3. DTO层（请求参数）
- `BusinessAssetQueryDto.java` - 商机资产统计查询DTO
- `BusinessAssetExportDto.java` - 商机资产统计导出DTO
- `AssetBuryingPointDto.java` - 资产埋点DTO

### 4. VO层（响应数据）
- `BusinessAssetStatVo.java` - 商机资产统计响应VO

### 5. Entity层（已存在）
- `BusinessAssetDayEntity.java` - 商机资产日表实体
- `BusinessAssetWeekEntity.java` - 商机资产周表实体
- `BusinessAssetMonthEntity.java` - 商机资产月表实体
- `BusinessAssetYearEntity.java` - 商机资产年表实体

### 6. Mapper层（已完整实现）
- `BusinessAssetMapper.java` - 商机资产数据访问接口（已添加新方法）
- `BusinessAssetMapper.xml` - 商机资产数据访问SQL实现（已完整实现）

### 7. 定时任务
- `BusinessAssetScheduler.java` - 商机资产数据汇聚定时任务

### 8. 实体类（新增）
- `BusinessAssetUpdEntity.java` - 商机资产启动记录表实体

## 接口说明

### 1. 商机数量统计接口

**接口地址**: `POST /api/system-manager/v1/uportal/asset/statBusiness`

**请求参数**:
```json
{
    "startTime": "20250101",
    "endTime": "20250131",
    "areaId": "area001",
    "timeType": 1,
    "timePoint": "20250115"
}
```

**参数说明**:
- `areaId`: 父节点地区ID，系统会自动查询所有子节点数据
- `timePoint`: 下级列表展示的具体时间点，必须在startTime和endTime范围内
- 接口同时返回汇总数据和下级数据，放在同一个dataList中

**响应数据**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "timeStatList": [{
            "timeNode": "20250101",
            "projectAddNum": 8,
            "projectStartNum": 3
        }, {
            "timeNode": "20250102",
            "projectAddNum": 10,
            "projectStartNum": 4
        }, {
            "timeNode": "20250103",
            "projectAddNum": 0,
            "projectStartNum": 0
        }],
        "areaStatList": [{
            "areaName": "ALL",
            "allNum": 15,
            "projectAddNum": 8,
            "bidNum": 5,
            "projectStartNum": 3,
            "subBidNum": 2,
            "beforeBidNum": 3,
            "projectApprovalNum": 2
        }, {
            "areaName": "北京市",
            "allNum": 8,
            "projectAddNum": 5,
            "bidNum": 3,
            "projectStartNum": 2,
            "subBidNum": 1,
            "beforeBidNum": 2,
            "projectApprovalNum": 1
        }, {
            "areaName": "上海市",
            "allNum": 7,
            "projectAddNum": 3,
            "bidNum": 2,
            "projectStartNum": 1,
            "subBidNum": 1,
            "beforeBidNum": 1,
            "projectApprovalNum": 1
        }]
    }
}
```

**数据说明**:
- `timeStatList`: 时间范围内每个时间节点的汇总数据（新增商机数和启动投标数）
  - `timeNode`: 时间节点（20250101、202501、2025等，根据timeType而定）
  - 没有数据的时间节点会自动补0
- `areaStatList`: 指定时间点各下级地区的详细统计数据
  - 第一行为"ALL"汇总行，包含所有下级地区数据的总和
  - 后续行为各个下级地区的详细数据

### 2. 商机统计数据导出接口

**接口地址**: `POST /api/system-manager/v1/uportal/asset/statBusinessExport`

### 3. 前端埋点接口

**接口地址**: `POST /api/system-manager/v1/uportal/asset/buryingPoint`

**请求参数**:
```json
{
    "resourceId": "project001",
    "typeId": "area001",
    "operationType": 4
}
```

## 已实现功能

### 1. Mapper层SQL实现 ✅
已在 `BusinessAssetMapper.xml` 中实现所有方法的SQL：
- ✅ `selectDayDateByAreaIdAndTimeRange` - 根据地区ID和时间范围查询日表数据
- ✅ `selectWeekDateByAreaIdAndTimeRange` - 根据地区ID和时间范围查询周表数据
- ✅ `selectMonthDateByAreaIdAndTimeRange` - 根据地区ID和时间范围查询月表数据
- ✅ `selectYearDateByAreaIdAndTimeRange` - 根据地区ID和时间范围查询年表数据
- ✅ `selectJuniorAreaBusinessData` - 查询下级地区商机统计数据（支持动态时间维度）
- ✅ `selectChildAreaDayDataByTimePoint` - 根据父节点ID和时间点查询子节点日表数据
- ✅ `selectChildAreaWeekDataByTimePoint` - 根据父节点ID和时间点查询子节点周表数据
- ✅ `selectChildAreaMonthDataByTimePoint` - 根据父节点ID和时间点查询子节点月表数据
- ✅ `selectChildAreaYearDataByTimePoint` - 根据父节点ID和时间点查询子节点年表数据
- ✅ `selectChildAreaDayDataByTimeRange` - 根据父节点ID和时间范围查询子节点日表汇总数据
- ✅ `selectChildAreaWeekDataByTimeRange` - 根据父节点ID和时间范围查询子节点周表汇总数据
- ✅ `selectChildAreaMonthDataByTimeRange` - 根据父节点ID和时间范围查询子节点月表汇总数据
- ✅ `selectChildAreaYearDataByTimeRange` - 根据父节点ID和时间范围查询子节点年表汇总数据
- ✅ `insertBusinessStartRecord` - 插入商机启动投标记录
- ✅ `batchInsertDayData` - 批量插入日表数据（支持ON DUPLICATE KEY UPDATE）
- ✅ `batchInsertWeekData` - 批量插入周表数据
- ✅ `batchInsertMonthData` - 批量插入月表数据
- ✅ `batchInsertYearData` - 批量插入年表数据
- ✅ `countBusinessStartRecordsByDay` - 统计指定日期的启动投标记录数量
- ✅ `countBusinessStartRecordsByTimeRange` - 统计时间范围内的启动投标记录数量
- ✅ `checkDayDataExists` - 检查日表数据是否存在
- ✅ `updateDayData` - 更新日表数据
- ✅ `selectSubAreaIds` - 递归查询下级地区ID（使用CTE递归查询）
- ✅ `selectAreaNameById` - 查询地区名称

### 2. Service层业务逻辑 ✅
已在 `BusinessAssetDomainServiceImpl.java` 中完整实现：
- ✅ `getJuniorBusinessData()` - 下级商机数据查询逻辑（按地区分组汇总）
- ✅ `aggregateBusinessAssetData()` - 数据汇聚逻辑（日→周→月→年）
- ✅ `exportBusinessAssetStat()` - 导出逻辑框架
- ✅ `processBusinessStartBuryingPoint()` - 埋点数据处理逻辑（同步更新统计数据）
- ✅ `updateDayStatistics()` - 更新当日统计数据
- ✅ `aggregateWeekData()` - 汇聚周数据
- ✅ `aggregateMonthData()` - 汇聚月数据
- ✅ `aggregateYearData()` - 汇聚年数据

### 3. 定时任务 ✅
- ✅ `BusinessAssetScheduler.java` - 每天凌晨1点自动执行数据汇聚

## 待完善功能

### 1. 与其他模块的集成
- 需要调用project-manager模块获取实时商机数据
- 需要确认地区管理表结构（当前假设为sys_area表）
- 需要完善Excel导出功能的具体实现

## 数据库表结构

根据方案文档，需要以下表：
- `asset_business_upd` - 商机资产启动记录表
- `asset_business_day` - 商机资产日表（已添加parent_area_id字段）
- `asset_business_week` - 商机资产周表（已添加parent_area_id字段）
- `asset_business_month` - 商机资产月表（已添加parent_area_id字段）
- `asset_business_year` - 商机资产年表（已添加parent_area_id字段）

### 数据库脚本位置
- **安装脚本**: `system-manager/src/main/resources/dbscript/install/system_manager.sql`
- **更新脚本**: `system-manager/src/main/resources/dbscript/update/system_manager_update.sql`

### 字段说明
所有商机资产表都包含以下关键字段：
- `area_id`: 当前地区ID
- `parent_area_id`: 父级地区ID（新增字段）
- 其他业务字段：all_num, bid_num, project_add_num, project_start_num等

## 注意事项

1. **日志使用英文**：所有日志信息使用英文，注释使用中文
2. **代码复杂度控制**：确保圈复杂度小于10
3. **性能考虑**：避免性能问题，合理使用索引和分页
4. **异步处理**：埋点接口采用异步执行
5. **数据完整性**：统计查询无数据的节点要返回0

## 核心特性

### 1. 完整的SQL实现
- **递归查询**：使用CTE递归查询实现地区层级关系
- **批量操作**：支持批量插入和更新，提高性能
- **数据完整性**：使用ON DUPLICATE KEY UPDATE确保数据一致性
- **动态查询**：支持根据时间类型动态选择不同的表进行查询
- **父子节点查询**：支持根据父节点ID查询所有子节点数据

### 2. 智能数据汇聚
- **自动汇聚**：定时任务自动将日表数据汇聚到周、月、年表
- **增量更新**：支持增量更新统计数据，避免全量重算
- **时间计算**：自动计算周、月的开始和结束时间
- **父节点继承**：汇聚时自动继承父节点ID

### 3. 分层数据结构
- **时间统计层**：`timeStatList` 显示时间范围内每个时间节点的汇总数据
- **地区统计层**：`areaStatList` 显示指定时间点各下级地区的详细数据
- **时间节点补全**：时间范围内没有数据的节点自动补0，确保数据完整性
- **汇总行**：地区数据自动添加ALL汇总行，展示所有子地区数据总和
- **时间点查询**：支持在时间范围内指定具体时间点查询下级数据

### 4. 异步处理
- **埋点异步**：埋点数据采用异步处理，不影响主业务流程
- **实时更新**：埋点触发时实时更新当日统计数据

### 5. 数据安全
- **参数验证**：完整的参数验证和异常处理
- **日志记录**：详细的操作日志，便于问题排查
- **事务支持**：关键操作支持事务，确保数据一致性

## 下一步工作

1. ✅ ~~实现Mapper层的SQL查询~~
2. ✅ ~~完善Service层的业务逻辑~~
3. 添加单元测试
4. 集成其他模块的服务调用
5. ✅ ~~实现定时任务和数据汇聚逻辑~~
6. 完善Excel导出功能
7. 优化SQL性能和索引设计

## 🔧 数据库更新说明

### 新系统安装
直接执行 `system-manager/src/main/resources/dbscript/install/system_manager.sql` 即可，已包含 `parent_area_id` 字段。

### 现有系统更新
执行 `system-manager/src/main/resources/dbscript/update/system_manager_update.sql` 中的更新脚本，会自动为现有表添加 `parent_area_id` 字段。

### 📈 性能优化

- 添加了 `parent_area_id` 相关索引
- 使用 `GROUP BY` 进行数据汇总
- 支持批量操作减少数据库交互

## 测试建议

建议编写单元测试来验证：
1. Controller层的参数验证
2. Service层的业务逻辑
3. Mapper层的SQL查询
4. 异步处理的正确性
5. 数据汇聚的准确性

## ✅ 完成状态

现在整个系统完全支持您提出的需求：
- ✅ 根据父节点查询所有子节点数据
- ✅ 分层数据结构：时间统计层 + 地区统计层
- ✅ 时间节点自动补全，没数据默认补0
- ✅ 时间范围内每个时间节点的新增商机数和启动投标数
- ✅ 指定时间点的下级地区详细统计数据
- ✅ 自动添加"ALL"汇总行
- ✅ 删除了junior参数，简化接口调用
- ✅ 数据库脚本已正确放置在标准目录中
