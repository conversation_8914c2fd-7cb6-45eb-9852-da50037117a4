package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 商机统计查询请求DTO
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Setter
@ToString
public class BusinessStatisticsQueryDto {

    /**
     * 开始日期，格式：20250715
     */
    @NotBlank(message = "Start date cannot be empty")
    @Pattern(regexp = "^\\d{8}$", message = "Start date format must be YYYYMMDD")
    private String startDate;

    /**
     * 结束日期，格式：20250715（可选，如果不传则只查询单个时间点）
     */
    @Pattern(regexp = "^\\d{8}$", message = "End date format must be YYYYMMDD")
    private String endDate;

    /**
     * 验证日期范围的有效性
     */
    public void validate() {
        if (endDate != null && !endDate.isEmpty()) {
            if (startDate.compareTo(endDate) > 0) {
                throw new IllegalArgumentException("Start date cannot be later than end date");
            }
        }
    }
}
