package com.zte.uedm.dcdigital.application.statistics.impl;

import com.zte.uedm.dcdigital.application.statistics.BusinessStatisticsService;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectAreaObj;
import com.zte.uedm.dcdigital.domain.service.BusinessStatisticsDomainService;
import com.zte.uedm.dcdigital.domain.service.ProjectAreaDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商机统计服务实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class BusinessStatisticsServiceImpl implements BusinessStatisticsService {

    @Autowired
    private BusinessStatisticsDomainService businessStatisticsDomainService;

    @Autowired
    private ProjectAreaDomainService projectAreaDomainService;

    @Override
    public List<BusinessStatisticsVo> getAreaBusinessStatistics(BusinessStatisticsQueryDto queryDto) {
        log.info("Starting area business statistics query with params: {}", queryDto);
        
        // 参数验证
        queryDto.validate();
        
        try {
            // 1. 生成日期范围列表
            List<String> dateList = generateDateList(queryDto.getStartDate(), queryDto.getEndDate());
            log.debug("Generated date list: {}", dateList);
            
            // 2. 获取所有地区及其层级关系
            List<ProjectAreaObj> allAreas = getAllAreasWithHierarchy();
            log.debug("Found {} areas", allAreas.size());
            
            // 3. 为每个日期和地区组合生成统计数据
            List<BusinessStatisticsVo> result = new ArrayList<>();
            
            for (String date : dateList) {
                // 按层级从底层开始处理地区数据
                Map<String, BusinessStatisticsVo> areaStatisticsMap = new HashMap<>();
                
                // 先处理最底层地区（叶子节点）
                List<ProjectAreaObj> leafAreas = getLeafAreas(allAreas);
                for (ProjectAreaObj area : leafAreas) {
                    BusinessStatisticsVo statistics = businessStatisticsDomainService
                            .calculateAreaStatistics(area.getId(), date);
                    statistics.setDay(date);
                    statistics.setAreaId(area.getId());
                    statistics.setAreaName(area.getAreaName());
                    areaStatisticsMap.put(area.getId(), statistics);
                }
                
                // 然后向上汇聚到父级地区
                aggregateToParentAreas(allAreas, areaStatisticsMap);
                
                // 添加到结果列表
                result.addAll(areaStatisticsMap.values());
            }
            
            log.info("Generated {} statistics records", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("Error occurred while generating business statistics", e);
            throw new RuntimeException("Failed to generate business statistics: " + e.getMessage(), e);
        }
    }

    /**
     * 生成日期范围列表
     */
    private List<String> generateDateList(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        
        if (endDate == null || endDate.isEmpty()) {
            // 只查询单个时间点
            dateList.add(startDate);
        } else {
            // 生成日期范围
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);
            
            LocalDate current = start;
            while (!current.isAfter(end)) {
                dateList.add(current.format(formatter));
                current = current.plusDays(1);
            }
        }
        
        return dateList;
    }

    /**
     * 获取所有地区及其层级关系
     */
    private List<ProjectAreaObj> getAllAreasWithHierarchy() {
        // 查询所有地区，传入空的地区ID列表和地区名称
        return projectAreaDomainService.selectAreaListBy(Collections.emptyList(), null);
    }

    /**
     * 获取叶子节点地区（最底层地区）
     */
    private List<ProjectAreaObj> getLeafAreas(List<ProjectAreaObj> allAreas) {
        Set<String> parentIds = allAreas.stream()
                .map(ProjectAreaObj::getParentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        return allAreas.stream()
                .filter(area -> !parentIds.contains(area.getId()))
                .collect(Collectors.toList());
    }

    /**
     * 向上汇聚数据到父级地区
     */
    private void aggregateToParentAreas(List<ProjectAreaObj> allAreas,
                                       Map<String, BusinessStatisticsVo> areaStatisticsMap) {
        // 构建父子关系映射
        Map<String, List<ProjectAreaObj>> childrenMap = allAreas.stream()
                .filter(area -> area.getParentId() != null && !area.getParentId().equals("root"))
                .collect(Collectors.groupingBy(ProjectAreaObj::getParentId));

        // 按层级从高到低排序，确保先处理子级再处理父级
        List<ProjectAreaObj> sortedAreas = allAreas.stream()
                .filter(area -> area.getParentId() != null && !area.getParentId().equals("root"))
                .sorted((a, b) -> Integer.compare(b.getAreaLevel(), a.getAreaLevel()))
                .collect(Collectors.toList());

        // 处理每个有子地区的父级地区
        Set<String> processedParents = new HashSet<>();
        for (ProjectAreaObj area : sortedAreas) {
            String parentId = area.getParentId();
            if (!processedParents.contains(parentId) && childrenMap.containsKey(parentId)) {
                ProjectAreaObj parentArea = allAreas.stream()
                        .filter(a -> a.getId().equals(parentId))
                        .findFirst()
                        .orElse(null);

                if (parentArea != null && !areaStatisticsMap.containsKey(parentId)) {
                    BusinessStatisticsVo parentStats = aggregateChildrenData(parentArea, childrenMap, areaStatisticsMap);
                    if (parentStats != null) {
                        areaStatisticsMap.put(parentId, parentStats);
                    }
                }
                processedParents.add(parentId);
            }
        }
    }

    /**
     * 汇聚子地区数据到父地区
     */
    private BusinessStatisticsVo aggregateChildrenData(ProjectAreaObj parentArea,
                                                      Map<String, List<ProjectAreaObj>> childrenMap,
                                                      Map<String, BusinessStatisticsVo> areaStatisticsMap) {
        List<ProjectAreaObj> children = childrenMap.get(parentArea.getId());
        if (children == null || children.isEmpty()) {
            return null; // 没有子地区，跳过
        }
        
        BusinessStatisticsVo aggregated = new BusinessStatisticsVo();
        aggregated.setAreaId(parentArea.getId());
        aggregated.setAreaName(parentArea.getAreaName());
        
        for (ProjectAreaObj child : children) {
            BusinessStatisticsVo childStats = areaStatisticsMap.get(child.getId());
            if (childStats != null) {
                aggregated.setAllNum(aggregated.getAllNum() + childStats.getAllNum());
                aggregated.setBidNum(aggregated.getBidNum() + childStats.getBidNum());
                aggregated.setSubBidNum(aggregated.getSubBidNum() + childStats.getSubBidNum());
                aggregated.setBeforeBidNum(aggregated.getBeforeBidNum() + childStats.getBeforeBidNum());
                aggregated.setProjectApprovalNum(aggregated.getProjectApprovalNum() + childStats.getProjectApprovalNum());
                aggregated.setProjectAddNum(aggregated.getProjectAddNum() + childStats.getProjectAddNum());
                aggregated.setProjectStartNum(aggregated.getProjectStartNum() + childStats.getProjectStartNum());
            }
        }
        
        return aggregated;
    }
}
