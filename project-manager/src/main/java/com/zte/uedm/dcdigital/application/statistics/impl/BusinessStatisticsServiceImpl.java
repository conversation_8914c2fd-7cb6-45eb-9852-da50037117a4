package com.zte.uedm.dcdigital.application.statistics.impl;

import com.zte.uedm.dcdigital.application.statistics.BusinessStatisticsService;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectAreaObj;
import com.zte.uedm.dcdigital.domain.service.BusinessStatisticsDomainService;
import com.zte.uedm.dcdigital.domain.service.ProjectAreaDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 商机统计服务实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class BusinessStatisticsServiceImpl implements BusinessStatisticsService {

    @Autowired
    private BusinessStatisticsDomainService businessStatisticsDomainService;

    @Autowired
    private ProjectAreaDomainService projectAreaDomainService;

    @Override
    public List<BusinessStatisticsVo> getAreaBusinessStatistics(BusinessStatisticsQueryDto queryDto) {
        log.info("Starting area business statistics query with params: {}", queryDto);
        
        // 参数验证
        queryDto.validate();
        
        try {
            // 1. 生成日期范围列表
            List<String> dateList = generateDateList(queryDto.getStartDate(), queryDto.getEndDate());
            log.debug("Generated date list: {}", dateList);
            
            // 2. 获取所有地区及其层级关系
            List<ProjectAreaObj> allAreas = getAllAreasWithHierarchy();
            log.debug("Found {} areas", allAreas.size());
            
            // 3. 为每个日期和地区组合生成统计数据
            List<BusinessStatisticsVo> result = new ArrayList<>();
            
            for (String date : dateList) {
                log.debug("Processing statistics for date: {}", date);

                // 为每个地区生成统计数据
                Map<String, BusinessStatisticsVo> areaStatisticsMap = new HashMap<>();

                // 处理所有地区，每个地区都直接查询其本身及子地区的数据
                for (ProjectAreaObj area : allAreas) {
                    BusinessStatisticsVo statistics = businessStatisticsDomainService
                            .calculateAreaStatistics(area.getId(), date);
                    statistics.setDay(date);
                    statistics.setAreaId(area.getId());
                    statistics.setAreaName(area.getAreaName());
                    areaStatisticsMap.put(area.getId(), statistics);

                    log.debug("Area {} statistics: allNum={}, projectAddNum={}, projectStartNum={}",
                             area.getId(), statistics.getAllNum(), statistics.getProjectAddNum(), statistics.getProjectStartNum());
                }

                // 添加到结果列表
                result.addAll(areaStatisticsMap.values());
            }
            
            log.info("Generated {} statistics records", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("Error occurred while generating business statistics", e);
            throw new RuntimeException("Failed to generate business statistics: " + e.getMessage(), e);
        }
    }

    /**
     * 生成日期范围列表
     */
    private List<String> generateDateList(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        
        if (endDate == null || endDate.isEmpty()) {
            // 只查询单个时间点
            dateList.add(startDate);
        } else {
            // 生成日期范围
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);
            
            LocalDate current = start;
            while (!current.isAfter(end)) {
                dateList.add(current.format(formatter));
                current = current.plusDays(1);
            }
        }
        
        return dateList;
    }

    /**
     * 获取所有地区及其层级关系
     */
    private List<ProjectAreaObj> getAllAreasWithHierarchy() {
        // 查询所有地区，传入空的地区ID列表和地区名称
        return projectAreaDomainService.selectAreaListBy(Collections.emptyList(), null);
    }


}
