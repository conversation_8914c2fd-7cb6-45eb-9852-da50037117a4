package com.zte.uedm.dcdigital.domain.service.impl;

import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectRepository;
import com.zte.uedm.dcdigital.domain.service.BusinessStatisticsDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BusinessStatisticsMapper;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 商机统计领域服务实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class BusinessStatisticsDomainServiceImpl implements BusinessStatisticsDomainService {

    @Autowired
    private BusinessStatisticsMapper businessStatisticsMapper;

    @Autowired
    private ProjectRepository projectRepository;

    @Override
    public BusinessStatisticsVo calculateAreaStatistics(String areaId, String date) {
        log.debug("Calculating statistics for area: {} on date: {}", areaId, date);
        
        BusinessStatisticsVo statistics = new BusinessStatisticsVo();
        statistics.setAreaId(areaId);
        
        try {
            // 获取该地区的所有子地区ID（包括自己）
            List<String> allAreaIds = businessStatisticsMapper.getAllSubAreaIds(areaId);
            
            // 转换日期格式为 YYYY-MM-DD
            String formattedDate = formatDateForQuery(date);
            
            // 1. 统计商机总数（截止到指定日期）
            Long allNum = businessStatisticsMapper.countProjectsByAreaIds(allAreaIds, formattedDate);
            statistics.setAllNum(allNum != null ? allNum : 0L);
            
            // 2. 统计各阶段商机数量（截止到指定日期的状态）
            Long bidNum = businessStatisticsMapper.countProjectsByStageAndAreaIds(allAreaIds, formattedDate, 3);
            statistics.setBidNum(bidNum != null ? bidNum : 0L);
            
            Long subBidNum = businessStatisticsMapper.countProjectsByStageAndAreaIds(allAreaIds, formattedDate, 4);
            statistics.setSubBidNum(subBidNum != null ? subBidNum : 0L);
            
            Long beforeBidNum = businessStatisticsMapper.countProjectsByStageAndAreaIds(allAreaIds, formattedDate, 2);
            statistics.setBeforeBidNum(beforeBidNum != null ? beforeBidNum : 0L);
            
            Long projectApprovalNum = businessStatisticsMapper.countProjectsByStageAndAreaIds(allAreaIds, formattedDate, 1);
            statistics.setProjectApprovalNum(projectApprovalNum != null ? projectApprovalNum : 0L);
            
            // 3. 统计指定日期新增的商机数量
            Long projectAddNum = businessStatisticsMapper.countProjectsAddedOnDate(allAreaIds, formattedDate);
            statistics.setProjectAddNum(projectAddNum != null ? projectAddNum : 0L);
            
            // 4. 统计指定日期启动投标的商机数量
            Long projectStartNum = businessStatisticsMapper.countProjectsStartedBiddingOnDate(allAreaIds, formattedDate);
            statistics.setProjectStartNum(projectStartNum != null ? projectStartNum : 0L);
            
            log.debug("Statistics calculated for area {}: {}", areaId, statistics);
            return statistics;
            
        } catch (Exception e) {
            log.error("Error calculating statistics for area: {} on date: {}", areaId, date, e);
            // 返回默认值，避免影响整体统计
            return statistics;
        }
    }

    /**
     * 将日期格式从 yyyyMMdd 转换为 yyyy-MM-dd
     */
    private String formatDateForQuery(String date) {
        if (date == null || date.length() != 8) {
            throw new IllegalArgumentException("Invalid date format: " + date);
        }
        
        try {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate localDate = LocalDate.parse(date, inputFormatter);
            return localDate.format(outputFormatter);
        } catch (Exception e) {
            log.error("Error formatting date: {}", date, e);
            throw new IllegalArgumentException("Invalid date format: " + date, e);
        }
    }
}
