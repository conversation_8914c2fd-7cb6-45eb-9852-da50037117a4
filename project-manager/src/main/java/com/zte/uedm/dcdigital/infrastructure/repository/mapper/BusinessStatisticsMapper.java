package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机统计数据访问接口
 * 
 * <AUTHOR> Assistant
 */
@Mapper
public interface BusinessStatisticsMapper {

    /**
     * 获取指定地区的所有子地区ID（包括自己）
     * 使用递归查询获取完整的地区层级
     * 
     * @param areaId 地区ID
     * @return 所有子地区ID列表
     */
    List<String> getAllSubAreaIds(@Param("areaId") String areaId);

    /**
     * 统计指定地区列表中截止到指定日期的商机总数
     * 
     * @param areaIds 地区ID列表
     * @param date 截止日期，格式：yyyy-MM-dd
     * @return 商机总数
     */
    Long countProjectsByAreaIds(@Param("areaIds") List<String> areaIds, @Param("date") String date);

    /**
     * 统计指定地区列表中截止到指定日期的特定阶段商机数量
     * 
     * @param areaIds 地区ID列表
     * @param date 截止日期，格式：yyyy-MM-dd
     * @param projectStage 项目阶段：1-立项阶段，2-标前阶段，3-投标阶段，4-交标阶段
     * @return 特定阶段商机数量
     */
    Long countProjectsByStageAndAreaIds(@Param("areaIds") List<String> areaIds, 
                                       @Param("date") String date, 
                                       @Param("projectStage") Integer projectStage);

    /**
     * 统计指定地区列表中在指定日期新增的商机数量
     * 
     * @param areaIds 地区ID列表
     * @param date 指定日期，格式：yyyy-MM-dd
     * @return 新增商机数量
     */
    Long countProjectsAddedOnDate(@Param("areaIds") List<String> areaIds, @Param("date") String date);

    /**
     * 统计指定地区列表中在指定日期启动投标的商机数量
     * 通过查询 launch_bidding 表的 create_time 字段
     * 
     * @param areaIds 地区ID列表
     * @param date 指定日期，格式：yyyy-MM-dd
     * @return 启动投标商机数量
     */
    Long countProjectsStartedBiddingOnDate(@Param("areaIds") List<String> areaIds, @Param("date") String date);
}
