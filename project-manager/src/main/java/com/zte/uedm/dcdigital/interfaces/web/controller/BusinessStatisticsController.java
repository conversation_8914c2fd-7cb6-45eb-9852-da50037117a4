package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.statistics.BusinessStatisticsService;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * 商机统计控制器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Controller
@Path("/uportal/business-statistics")
@Api(value = "商机统计", tags = {"商机统计"})
public class BusinessStatisticsController {

    @Autowired
    private BusinessStatisticsService businessStatisticsService;

    /**
     * 获取所有地区下级商机统计数据
     * 
     * 传入天类型时间节点范围或者天单个时间节点，查询时间范围内或者截止到该时间点的下级商机列表数据。
     * 这个接口不能直接查询商机的日周月年表，需要通过现有商机业务逻辑去project模块的表中将数据查询整合出来，
     * 地区需要递归去处理，先查最底层地区的数据，然后汇聚给父节点地区，一直这样操作直到汇聚到最顶层，
     * 最后输出一个所有地区的统计数据列表，这个列表数据能要直接入库到商机资产日表中。
     * 
     * @param queryDto 查询条件
     * @return 所有地区的商机统计数据列表
     */
    @POST
    @Path("/area-business-data")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取所有地区下级商机统计数据", notes = "传入时间节点或时间范围，查询所有地区的商机统计数据", httpMethod = "POST")
    public BaseResult<List<BusinessStatisticsVo>> getAreaBusinessStatistics(@Valid BusinessStatisticsQueryDto queryDto) {
        log.info("Received business statistics query request: {}", queryDto);
        
        try {
            // 参数验证
            queryDto.validate();
            
            // 调用服务获取统计数据
            List<BusinessStatisticsVo> result = businessStatisticsService.getAreaBusinessStatistics(queryDto);
            
            log.info("Successfully generated {} business statistics records", result.size());
            return BaseResult.success(result);
            
        } catch (IllegalArgumentException e) {
            log.warn("Invalid request parameters: {}", e.getMessage());
            return BaseResult.failed( "Invalid parameters: " + e.getMessage());
            
        } catch (Exception e) {
            log.error("Error occurred while processing business statistics request", e);
            return BaseResult.failed("Internal server error: " + e.getMessage());
        }
    }
}
