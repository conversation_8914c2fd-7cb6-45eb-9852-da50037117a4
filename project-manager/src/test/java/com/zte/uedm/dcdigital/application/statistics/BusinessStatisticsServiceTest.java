package com.zte.uedm.dcdigital.application.statistics;

import com.zte.uedm.dcdigital.application.statistics.impl.BusinessStatisticsServiceImpl;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectAreaObj;
import com.zte.uedm.dcdigital.domain.service.BusinessStatisticsDomainService;
import com.zte.uedm.dcdigital.domain.service.ProjectAreaDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticsVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 商机统计服务测试类
 * 
 * <AUTHOR> Assistant
 */
@RunWith(MockitoJUnitRunner.class)
public class BusinessStatisticsServiceTest {

    @Mock
    private BusinessStatisticsDomainService businessStatisticsDomainService;

    @Mock
    private ProjectAreaDomainService projectAreaDomainService;

    @InjectMocks
    private BusinessStatisticsServiceImpl businessStatisticsService;

    private BusinessStatisticsQueryDto queryDto;
    private List<ProjectAreaObj> mockAreas;

    @Before
    public void setUp() {
        queryDto = new BusinessStatisticsQueryDto();
        queryDto.setStartDate("20250715");

        // 创建模拟地区数据
        ProjectAreaObj rootArea = new ProjectAreaObj();
        rootArea.setId("root");
        rootArea.setAreaName("所有地区");
        rootArea.setParentId(null);
        rootArea.setAreaLevel(0);

        ProjectAreaObj area1 = new ProjectAreaObj();
        area1.setId("area1");
        area1.setAreaName("地区1");
        area1.setParentId("root");
        area1.setAreaLevel(1);

        ProjectAreaObj area2 = new ProjectAreaObj();
        area2.setId("area2");
        area2.setAreaName("地区2");
        area2.setParentId("area1");
        area2.setAreaLevel(2);

        mockAreas = Arrays.asList(rootArea, area1, area2);
    }

    @Test
    public void testGetAreaBusinessStatistics_SingleDate() {
        // 准备测试数据
        when(projectAreaDomainService.selectAreaListBy(eq(Collections.emptyList()), isNull()))
                .thenReturn(mockAreas);

        BusinessStatisticsVo mockStats = new BusinessStatisticsVo();
        mockStats.setAllNum(5L);
        mockStats.setBidNum(2L);
        mockStats.setProjectAddNum(1L);
        mockStats.setProjectStartNum(1L);

        when(businessStatisticsDomainService.calculateAreaStatistics(anyString(), anyString()))
                .thenReturn(mockStats);

        // 执行测试
        List<BusinessStatisticsVo> result = businessStatisticsService.getAreaBusinessStatistics(queryDto);

        // 验证结果
        assertNotNull("Result should not be null", result);
        assertFalse("Result should not be empty", result.isEmpty());
        
        // 验证每个结果都有正确的日期
        for (BusinessStatisticsVo vo : result) {
            assertEquals("Date should match", "20250715", vo.getDay());
            assertNotNull("Area ID should not be null", vo.getAreaId());
            assertNotNull("Area name should not be null", vo.getAreaName());
        }
    }

    @Test
    public void testGetAreaBusinessStatistics_DateRange() {
        // 准备测试数据
        queryDto.setEndDate("20250717");
        
        when(projectAreaDomainService.selectAreaListBy(eq(Collections.emptyList()), isNull()))
                .thenReturn(mockAreas);

        BusinessStatisticsVo mockStats = new BusinessStatisticsVo();
        mockStats.setAllNum(5L);
        when(businessStatisticsDomainService.calculateAreaStatistics(anyString(), anyString()))
                .thenReturn(mockStats);

        // 执行测试
        List<BusinessStatisticsVo> result = businessStatisticsService.getAreaBusinessStatistics(queryDto);

        // 验证结果
        assertNotNull("Result should not be null", result);
        
        // 应该有3天 * 3个地区 = 9条记录
        int expectedRecords = 3 * mockAreas.size();
        assertEquals("Should have records for 3 days and 3 areas", expectedRecords, result.size());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetAreaBusinessStatistics_InvalidDateRange() {
        // 设置无效的日期范围（开始日期晚于结束日期）
        queryDto.setStartDate("20250717");
        queryDto.setEndDate("20250715");

        // 执行测试，应该抛出异常
        businessStatisticsService.getAreaBusinessStatistics(queryDto);
    }

    @Test
    public void testValidateQueryDto() {
        // 测试有效的查询参数
        BusinessStatisticsQueryDto validDto = new BusinessStatisticsQueryDto();
        validDto.setStartDate("20250715");
        validDto.setEndDate("20250717");
        
        // 不应该抛出异常
        validDto.validate();
        
        // 测试无效的日期范围
        BusinessStatisticsQueryDto invalidDto = new BusinessStatisticsQueryDto();
        invalidDto.setStartDate("20250717");
        invalidDto.setEndDate("20250715");
        
        try {
            invalidDto.validate();
            fail("Should throw IllegalArgumentException for invalid date range");
        } catch (IllegalArgumentException e) {
            assertTrue("Exception message should mention date range", 
                      e.getMessage().contains("Start date cannot be later than end date"));
        }
    }
}
