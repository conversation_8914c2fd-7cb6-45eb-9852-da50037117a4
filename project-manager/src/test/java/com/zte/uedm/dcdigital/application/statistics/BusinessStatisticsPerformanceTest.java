package com.zte.uedm.dcdigital.application.statistics;

import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticsVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;

/**
 * 商机统计服务性能测试
 * 
 * <AUTHOR> Assistant
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BusinessStatisticsPerformanceTest {

    @Autowired
    private BusinessStatisticsService businessStatisticsService;

    @Test
    public void testSingleDatePerformance() {
        // 测试单个日期查询性能
        BusinessStatisticsQueryDto queryDto = new BusinessStatisticsQueryDto();
        queryDto.setStartDate("20250320");
        
        long startTime = System.currentTimeMillis();
        List<BusinessStatisticsVo> result = businessStatisticsService.getAreaBusinessStatistics(queryDto);
        long endTime = System.currentTimeMillis();
        
        assertNotNull("Result should not be null", result);
        assertFalse("Result should not be empty", result.isEmpty());
        
        System.out.println("Single date query performance:");
        System.out.println("- Time taken: " + (endTime - startTime) + "ms");
        System.out.println("- Records generated: " + result.size());
        System.out.println("- Average time per record: " + String.format("%.2f", (double)(endTime - startTime) / result.size()) + "ms");
        
        // 验证数据正确性
        result.forEach(vo -> {
            assertEquals("Date should match", "20250320", vo.getDay());
            assertNotNull("Area ID should not be null", vo.getAreaId());
            assertNotNull("Area name should not be null", vo.getAreaName());
            assertTrue("All numbers should be non-negative", 
                      vo.getAllNum() >= 0 && vo.getProjectAddNum() >= 0 && vo.getProjectStartNum() >= 0);
        });
    }

    @Test
    public void testDateRangePerformance() {
        // 测试日期范围查询性能
        BusinessStatisticsQueryDto queryDto = new BusinessStatisticsQueryDto();
        queryDto.setStartDate("20250318");
        queryDto.setEndDate("20250321");
        
        long startTime = System.currentTimeMillis();
        List<BusinessStatisticsVo> result = businessStatisticsService.getAreaBusinessStatistics(queryDto);
        long endTime = System.currentTimeMillis();
        
        assertNotNull("Result should not be null", result);
        assertFalse("Result should not be empty", result.isEmpty());
        
        System.out.println("Date range query performance:");
        System.out.println("- Time taken: " + (endTime - startTime) + "ms");
        System.out.println("- Records generated: " + result.size());
        System.out.println("- Date range: 4 days");
        System.out.println("- Average time per record: " + String.format("%.2f", (double)(endTime - startTime) / result.size()) + "ms");
        
        // 验证日期范围
        String[] expectedDates = {"20250318", "20250319", "20250320", "20250321"};
        for (String expectedDate : expectedDates) {
            boolean dateExists = result.stream().anyMatch(vo -> expectedDate.equals(vo.getDay()));
            assertTrue("Date " + expectedDate + " should exist in results", dateExists);
        }
        
        // 打印每个日期的统计摘要
        for (String date : expectedDates) {
            List<BusinessStatisticsVo> dateResults = result.stream()
                    .filter(vo -> date.equals(vo.getDay()))
                    .collect(java.util.stream.Collectors.toList());
            
            long totalProjects = dateResults.stream().mapToLong(BusinessStatisticsVo::getAllNum).sum();
            long totalAdded = dateResults.stream().mapToLong(BusinessStatisticsVo::getProjectAddNum).sum();
            long totalStarted = dateResults.stream().mapToLong(BusinessStatisticsVo::getProjectStartNum).sum();
            
            System.out.println(String.format("Date %s: %d areas, total projects: %d, added: %d, started: %d", 
                              date, dateResults.size(), totalProjects, totalAdded, totalStarted));
        }
    }

    @Test
    public void testLargeDatasetPerformance() {
        // 测试较大数据集的性能（一个月的数据）
        BusinessStatisticsQueryDto queryDto = new BusinessStatisticsQueryDto();
        queryDto.setStartDate("20250301");
        queryDto.setEndDate("20250331");
        
        long startTime = System.currentTimeMillis();
        List<BusinessStatisticsVo> result = businessStatisticsService.getAreaBusinessStatistics(queryDto);
        long endTime = System.currentTimeMillis();
        
        assertNotNull("Result should not be null", result);
        
        System.out.println("Large dataset query performance:");
        System.out.println("- Time taken: " + (endTime - startTime) + "ms");
        System.out.println("- Records generated: " + result.size());
        System.out.println("- Date range: 31 days");
        System.out.println("- Average time per record: " + String.format("%.2f", (double)(endTime - startTime) / result.size()) + "ms");
        
        // 性能基准检查（根据实际情况调整）
        assertTrue("Query should complete within reasonable time", (endTime - startTime) < 30000); // 30秒内完成
        
        // 验证数据完整性
        long uniqueDates = result.stream().map(BusinessStatisticsVo::getDay).distinct().count();
        assertTrue("Should have data for multiple dates", uniqueDates > 1);
        
        long uniqueAreas = result.stream().map(BusinessStatisticsVo::getAreaId).distinct().count();
        assertTrue("Should have data for multiple areas", uniqueAreas > 1);
    }

    @Test
    public void testConcurrentAccess() {
        // 测试并发访问性能
        BusinessStatisticsQueryDto queryDto = new BusinessStatisticsQueryDto();
        queryDto.setStartDate("20250320");
        queryDto.setEndDate("20250321");
        
        int threadCount = 5;
        Thread[] threads = new Thread[threadCount];
        long[] executionTimes = new long[threadCount];
        
        long overallStartTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                long threadStartTime = System.currentTimeMillis();
                List<BusinessStatisticsVo> result = businessStatisticsService.getAreaBusinessStatistics(queryDto);
                long threadEndTime = System.currentTimeMillis();
                
                executionTimes[threadIndex] = threadEndTime - threadStartTime;
                assertNotNull("Result should not be null", result);
                assertFalse("Result should not be empty", result.isEmpty());
            });
            threads[i].start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                fail("Thread was interrupted");
            }
        }
        
        long overallEndTime = System.currentTimeMillis();
        
        System.out.println("Concurrent access performance:");
        System.out.println("- Overall time: " + (overallEndTime - overallStartTime) + "ms");
        System.out.println("- Thread count: " + threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            System.out.println("- Thread " + (i + 1) + " time: " + executionTimes[i] + "ms");
        }
        
        double avgExecutionTime = java.util.Arrays.stream(executionTimes).average().orElse(0.0);
        System.out.println("- Average execution time: " + String.format("%.2f", avgExecutionTime) + "ms");
    }
}
