package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.infrastructure.repository.dto.LaunchBiddingDataDto;
import com.zte.uedm.dcdigital.infrastructure.repository.dto.ProjectDataDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 商机统计 Mapper 测试类（性能优化版本）
 * 
 * <AUTHOR> Assistant
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BusinessStatisticsMapperTest {

    @Autowired
    private BusinessStatisticsMapper businessStatisticsMapper;

    @Test
    public void testGetAllSubAreaIds() {
        // 测试递归查询地区ID
        List<String> areaIds = businessStatisticsMapper.getAllSubAreaIds("root");
        assertNotNull("Area IDs should not be null", areaIds);
        assertFalse("Area IDs should not be empty", areaIds.isEmpty());
        assertTrue("Should contain root area", areaIds.contains("root"));
        
        System.out.println("All sub area IDs for root: " + areaIds);
    }

    @Test
    public void testSelectAllProjectsInDateRange() {
        // 测试批量查询项目数据
        String startDate = "2025-03-18";
        String endDate = "2025-03-21";
        
        List<ProjectDataDto> projects = businessStatisticsMapper.selectAllProjectsInDateRange(startDate, endDate);
        assertNotNull("Projects should not be null", projects);
        
        System.out.println("Found " + projects.size() + " projects in date range " + startDate + " to " + endDate);
        
        // 打印前几条数据用于验证
        projects.stream().limit(5).forEach(project -> {
            System.out.println(String.format("Project: id=%s, areaId=%s, stage=%d, createDate=%s", 
                              project.getId(), project.getAreaId(), project.getProjectStage(), project.getCreateDate()));
        });
    }

    @Test
    public void testSelectAllLaunchBiddingInDateRange() {
        // 测试批量查询启动投标数据
        String startDate = "2025-03-18";
        String endDate = "2025-03-21";
        
        List<LaunchBiddingDataDto> launchBiddings = businessStatisticsMapper.selectAllLaunchBiddingInDateRange(startDate, endDate);
        assertNotNull("Launch biddings should not be null", launchBiddings);
        
        System.out.println("Found " + launchBiddings.size() + " launch biddings in date range " + startDate + " to " + endDate);
        
        // 打印前几条数据用于验证
        launchBiddings.stream().limit(5).forEach(bidding -> {
            System.out.println(String.format("Launch bidding: id=%s, areaId=%s, createDate=%s", 
                              bidding.getId(), bidding.getAreaId(), bidding.getCreateDate()));
        });
    }

    @Test
    public void testPerformanceComparison() {
        // 性能对比测试：批量查询 vs 循环查询
        String startDate = "2025-03-18";
        String endDate = "2025-03-21";
        
        // 批量查询性能测试
        long batchStartTime = System.currentTimeMillis();
        List<ProjectDataDto> allProjects = businessStatisticsMapper.selectAllProjectsInDateRange(startDate, endDate);
        List<LaunchBiddingDataDto> allLaunchBiddings = businessStatisticsMapper.selectAllLaunchBiddingInDateRange(startDate, endDate);
        long batchEndTime = System.currentTimeMillis();
        
        System.out.println("Batch query time: " + (batchEndTime - batchStartTime) + "ms");
        System.out.println("Batch query results: " + allProjects.size() + " projects, " + allLaunchBiddings.size() + " launch biddings");
        
        // 循环查询性能测试（模拟原来的方式）
        long loopStartTime = System.currentTimeMillis();
        List<String> areaIds = Arrays.asList("root");
        String[] dates = {"2025-03-18", "2025-03-19", "2025-03-20", "2025-03-21"};
        
        int totalQueries = 0;
        for (String date : dates) {
            Long addedCount = businessStatisticsMapper.countProjectsAddedOnDate(areaIds, date);
            Long startedCount = businessStatisticsMapper.countProjectsStartedBiddingOnDate(areaIds, date);
            Long totalCount = businessStatisticsMapper.countAllProjectsByAreaIds(areaIds, date);
            totalQueries += 3;
        }
        long loopEndTime = System.currentTimeMillis();
        
        System.out.println("Loop query time: " + (loopEndTime - loopStartTime) + "ms");
        System.out.println("Loop query executed: " + totalQueries + " SQL statements");
        
        // 性能提升计算
        double improvement = (double)(loopEndTime - loopStartTime) / (batchEndTime - batchStartTime);
        System.out.println("Performance improvement: " + String.format("%.2f", improvement) + "x faster");
    }
}
