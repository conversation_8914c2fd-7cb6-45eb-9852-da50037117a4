# 商机统计接口说明

## 概述

本文档描述了商机统计功能的实现，该功能用于统计所有地区的商机数据，支持按时间范围查询，数据可直接入库到商机资产日表中。

## 接口信息

### 接口地址
```
POST /api/project-manager/v1/uportal/business-statistics/area-business-data
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| startDate | String | 是 | 开始日期，格式：YYYYMMDD | "20250715" |
| endDate | String | 否 | 结束日期，格式：YYYYMMDD，不传则只查询单个时间点 | "20250717" |

### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| day | String | 日期（例：20250620） |
| areaId | String | 地区ID |
| areaName | String | 地区名称 |
| allNum | Long | 当前的按区域的商机总数 |
| bidNum | Long | 处于投标阶段的商机总数 |
| subBidNum | Long | 处于交标阶段的商机总数 |
| beforeBidNum | Long | 处于标前阶段的商机总数 |
| projectApprovalNum | Long | 处于立项阶段的商机总数 |
| projectAddNum | Long | 周期内新增的商机总数 |
| projectStartNum | Long | 周期内新增的启动了投标的商机总数 |

## 业务逻辑说明

### 1. 地区层级处理
- 系统会递归查询所有地区，包括父子关系
- 从最底层地区开始统计数据
- 然后向上汇聚到父级地区
- 最终输出所有地区的统计数据

### 2. 商机阶段定义
- 1: 立项阶段 (BUSINESS_OPPORTUNITY)
- 2: 标前阶段 (BEFORE_BIDDING)  
- 3: 投标阶段 (BIDDING_STAGE)
- 4: 交标阶段 (DELIVER_STAGE)

### 3. 统计规则
- **商机总数**: 截止到指定日期创建的所有商机数量
- **各阶段商机数**: 截止到指定日期处于特定阶段的商机数量
- **新增商机数**: 在指定日期当天创建的商机数量
- **启动投标数**: 在指定日期当天启动投标的商机数量（通过 launch_bidding 表的 create_time 判断）

### 4. 时间范围处理
- 如果只传 startDate，查询截止到该时间点的数据
- 如果传时间范围，生成范围内每一天的所有地区统计数据
- 例如：查询1-3号，会生成3天 × 地区数量的统计记录

## 请求示例

### 查询单个时间点
```json
{
  "startDate": "20250715"
}
```

### 查询时间范围
```json
{
  "startDate": "20250715",
  "endDate": "20250717"
}
```

## 响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "day": "20250715",
      "areaId": "area001",
      "areaName": "华北地区",
      "allNum": 15,
      "bidNum": 5,
      "subBidNum": 3,
      "beforeBidNum": 4,
      "projectApprovalNum": 3,
      "projectAddNum": 2,
      "projectStartNum": 1
    },
    {
      "day": "20250715",
      "areaId": "area002",
      "areaName": "华南地区",
      "allNum": 12,
      "bidNum": 4,
      "subBidNum": 2,
      "beforeBidNum": 3,
      "projectApprovalNum": 3,
      "projectAddNum": 1,
      "projectStartNum": 1
    }
  ]
}
```

## 性能考虑

1. **批量查询**: 使用批量查询减少数据库访问次数
2. **递归优化**: 使用 CTE 递归查询优化地区层级查询
3. **索引建议**: 
   - project 表的 area_id, create_time, project_stage 字段建议建立复合索引
   - launch_bidding 表的 create_time 字段建议建立索引
   - project_area 表的 parent_id 字段建议建立索引

## 错误处理

- 400: 参数错误（日期格式错误、日期范围无效等）
- 500: 服务器内部错误

## 注意事项

1. 日期格式必须为 YYYYMMDD
2. 时间范围查询时，开始日期不能晚于结束日期
3. 数据量较大时建议分批查询，避免一次查询过多天数
4. 返回的数据可直接用于入库到商机资产日表中
