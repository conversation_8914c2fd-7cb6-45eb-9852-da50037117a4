# 商机统计接口说明

## 概述

本文档描述了商机统计功能的实现，该功能用于统计所有地区的商机数据，支持按时间范围查询，数据可直接入库到商机资产日表中。

## 接口信息

### 接口地址
```
POST /api/project-manager/v1/uportal/business-statistics/area-business-data
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| startDate | String | 条件必填 | 开始日期，格式：YYYYMMDD | "20250715" |
| endDate | String | 条件必填 | 结束日期，格式：YYYYMMDD | "20250717" |

**参数规则**：
- 如果只传 `startDate`：查询该日期的统计数据
- 如果只传 `endDate`：查询从第一个商机创建时间到该日期的所有统计数据
- 如果都传：查询指定日期范围的统计数据
- 如果都不传：返回参数错误

### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| day | String | 日期（例：20250620） |
| areaId | String | 地区ID |
| areaName | String | 地区名称 |
| allNum | Long | 当前的按区域的商机总数 |
| bidNum | Long | 处于投标阶段的商机总数 |
| subBidNum | Long | 处于交标阶段的商机总数 |
| beforeBidNum | Long | 处于标前阶段的商机总数 |
| projectApprovalNum | Long | 处于立项阶段的商机总数 |
| projectAddNum | Long | 周期内新增的商机总数 |
| projectStartNum | Long | 周期内新增的启动了投标的商机总数 |

## 业务逻辑说明

### 1. 地区层级处理
- 系统会递归查询所有地区，包括父子关系
- 从最底层地区开始统计数据
- 然后向上汇聚到父级地区
- 最终输出所有地区的统计数据

### 2. 商机阶段定义
- 1: 立项阶段 (BUSINESS_OPPORTUNITY)
- 2: 标前阶段 (BEFORE_BIDDING)  
- 3: 投标阶段 (BIDDING_STAGE)
- 4: 交标阶段 (DELIVER_STAGE)

### 3. 统计规则

#### 累计统计字段（截止到指定日期的累计数据）
- **allNum（商机总数）**: 截止到指定日期创建的所有商机数量，不限制阶段
- **bidNum（投标阶段商机数）**: 截止到指定日期当前处于投标阶段（project_stage=3）的商机数量
- **subBidNum（交标阶段商机数）**: 截止到指定日期当前处于交标阶段（project_stage=4）的商机数量
- **beforeBidNum（标前阶段商机数）**: 截止到指定日期当前处于标前阶段（project_stage=2）的商机数量
- **projectApprovalNum（立项阶段商机数）**: 截止到指定日期当前处于立项阶段（project_stage=1）的商机数量

#### 当日统计字段（指定日期当天的数据）
- **projectAddNum（新增商机数）**: 在指定日期当天创建的商机数量
- **projectStartNum（启动投标数）**: 在指定日期当天启动投标的商机数量（通过 launch_bidding 表的 create_time 判断）

### 4. 时间范围处理
- 如果只传 startDate，查询截止到该时间点的数据
- 如果传时间范围，生成范围内每一天的所有地区统计数据
- 例如：查询1-3号，会生成3天 × 地区数量的统计记录

## 请求示例

### 查询单个时间点
```json
{
  "startDate": "20250715"
}
```

### 查询时间范围
```json
{
  "startDate": "20250715",
  "endDate": "20250717"
}
```

### 查询从第一个商机到指定日期（新功能）
```json
{
  "endDate": "20250717"
}
```
**说明**：如果系统中第一个商机创建于 2025-01-01，则会查询 2025-01-01 到 2025-07-17 的所有统计数据

## 响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "day": "20250715",
      "areaId": "area001",
      "areaName": "华北地区",
      "allNum": 15,
      "bidNum": 5,
      "subBidNum": 3,
      "beforeBidNum": 4,
      "projectApprovalNum": 3,
      "projectAddNum": 2,
      "projectStartNum": 1
    },
    {
      "day": "20250715",
      "areaId": "area002",
      "areaName": "华南地区",
      "allNum": 12,
      "bidNum": 4,
      "subBidNum": 2,
      "beforeBidNum": 3,
      "projectApprovalNum": 3,
      "projectAddNum": 1,
      "projectStartNum": 1
    }
  ]
}
```

## 性能优化架构

### 优化前 vs 优化后

**优化前（v1.1）**：
- 查询次数：日期数 × 地区数 × 7种查询 = N × M × 7 次SQL
- 处理方式：串行循环处理
- 示例：4天 × 10地区 × 7查询 = 280次SQL

**优化后（v1.2）**：
- 查询次数：2次批量查询（项目数据 + 启动投标数据）
- 处理方式：并发Stream处理
- 示例：4天 × 10地区 = 仅2次SQL + 40个并发计算任务

### 核心优化策略

1. **批量数据预加载**:
   ```java
   // 查询截止到最后日期的所有项目数据（用于累计统计）
   List<ProjectDataDto> allProjects = mapper.selectAllProjectsUpToDate(endDate);
   // 查询时间范围内的启动投标数据（用于当日统计）
   List<LaunchBiddingDataDto> allLaunchBiddings = mapper.selectAllLaunchBiddingInDateRange(startDate, endDate);
   ```

2. **并发流处理**:
   ```java
   // 使用并发流处理所有日期和地区组合
   List<BusinessStatisticsVo> result = dateList.parallelStream()
       .flatMap(date -> allAreas.parallelStream()
           .map(area -> calculateFromMemoryData(area, date, allProjects, allLaunchBiddings)))
       .collect(Collectors.toList());
   ```

3. **内存计算替代数据库查询**:
   ```java
   // 累计统计：截止到指定日期的所有历史数据
   long allNum = areaProjects.stream()
       .filter(project -> project.getCreateDate().compareTo(formattedDate) <= 0)
       .count();

   // 当日统计：只统计指定日期当天的数据
   long projectAddNum = areaProjects.stream()
       .filter(project -> project.getCreateDate().equals(formattedDate))
       .count();
   ```

### 重要逻辑说明

**为什么不能限制项目数据的开始时间？**

- **累计统计字段**（allNum, bidNum, subBidNum, beforeBidNum, projectApprovalNum）需要统计截止到指定日期的所有历史数据
- **当日统计字段**（projectAddNum, projectStartNum）只需要统计指定日期当天的数据

**示例说明**：
- 查询 2025-03-20 的商机总数，需要统计从系统开始到 2025-03-20 的所有商机
- 查询 2025-03-20 的新增商机数，只需要统计 2025-03-20 当天创建的商机

**查询策略**：
- 项目数据：`WHERE create_time <= endDate`（包含所有历史数据）
- 启动投标数据：`WHERE create_time BETWEEN startDate AND endDate`（只包含时间范围内的数据）

### 性能提升效果

- **查询次数减少**: 从 O(N×M×K) 降低到 O(1)
- **并发处理**: 利用多核CPU并行计算
- **内存效率**: 减少数据库连接开销
- **可扩展性**: 支持更大的日期范围和地区数量

### 索引建议

- project 表的 area_id, create_time, project_stage 字段建议建立复合索引
- launch_bidding 表的 create_time 字段建议建立索引
- project_area 表的 parent_id 字段建议建立索引
- 考虑为 create_time 字段的前10位（日期部分）建立函数索引以优化批量查询性能

## 错误处理

- 400: 参数错误（日期格式错误、日期范围无效等）
- 500: 服务器内部错误

## 数据库兼容性

本接口针对 PostgreSQL 数据库进行了优化，考虑了以下特点：

1. **时间字段类型**: 数据库中的 create_time 字段为 TEXT 类型，格式为 'YYYY-MM-DD HH24:MI:SS'
2. **日期比较**: 使用 SUBSTRING 函数提取日期部分进行比较，避免类型转换错误
3. **字段命名**: 使用下划线命名规范（area_id, project_stage, create_time）

## 版本更新说明

### v1.3 智能日期范围功能
1. **智能开始日期**: 如果不传开始日期，自动使用第一个商机的创建时间作为开始日期
2. **灵活参数组合**: 支持三种参数组合方式
   - 只传开始日期：查询单个时间点
   - 只传结束日期：从第一个商机到指定日期
   - 都传：查询指定日期范围
3. **参数验证优化**: 更灵活的参数验证逻辑，支持条件必填

### v1.2 性能优化重构
1. **批量数据查询**: 改为先一次性查询所有需要的数据，避免在循环中执行SQL
2. **并发流处理**: 使用 Java 8 的 parallelStream 并发处理日期和地区组合
3. **内存计算优化**: 将统计逻辑从数据库查询改为内存中的 Stream 操作
4. **查询次数优化**: 从 O(日期数 × 地区数 × 查询类型数) 降低到 O(1) 次数据库查询
5. **修复查询范围逻辑**:
   - 累计统计字段：查询截止到指定日期的所有历史数据（不限制开始时间）
   - 当日统计字段：只查询时间范围内的数据

### v1.1 修复问题
1. **修复商机总数统计错误**: 商机总数现在正确统计截止到指定日期的所有商机，不再限制为当天创建的商机
2. **修复地区数据查询问题**: 改进地区数据处理逻辑，确保所有地区的数据都能正确查询和统计
3. **明确字段统计逻辑**: 区分累计统计字段和当日统计字段，确保统计逻辑正确
4. **优化递归查询**: 每个地区直接查询其本身及所有子地区的数据，避免数据遗漏

## 注意事项

1. 日期格式必须为 YYYYMMDD
2. 时间范围查询时，开始日期不能晚于结束日期
3. 数据量较大时建议分批查询，避免一次查询过多天数
4. 返回的数据可直接用于入库到商机资产日表中
5. 确保数据库中的时间字段格式为 'YYYY-MM-DD HH24:MI:SS'
